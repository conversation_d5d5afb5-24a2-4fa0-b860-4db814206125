# **App Name**: WorkoutWise Login

## Core Features:

- Login Form: Email and password input fields with validation to ensure correct format and non-empty fields.
- Social Login: Social login buttons (Google/Facebook) to initiate OAuth 2.0 flow.
- Error Handling: Clear error messages displayed for incorrect credentials or other login issues.
- Authentication Flow: Handles redirection to the dashboard upon successful authentication. Provides link to registration page

## Style Guidelines:

- Use shadcn/ui to build ui. use dark theme
- Use clear and readable typography for form labels and error messages.
- Use simple, outline-style icons for social login buttons (Google, Facebook) to maintain a clean look.
- Center the login form on the page to draw the user's attention and ensure easy access on various screen sizes.
- Use subtle transitions and animations for form elements (e.g., focusing on an input field) to enhance the user experience.