
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-sans), Arial, Helvetica, sans-serif; /* Ensure font-family uses the variable */
}

@layer base {
  :root {
    --background: 240 10% 3.9%; /* Dark Blue/Gray */
    --foreground: 0 0% 98%; /* Almost White */
    --card: 240 5% 10%; /* Slightly Lighter Dark Blue/Gray */
    --card-foreground: 0 0% 98%;
    --popover: 240 5% 10%;
    --popover-foreground: 0 0% 98%;
    --primary: 217.2 91.2% 59.8%; /* Vibrant Blue for button backgrounds, etc. */
    --primary-foreground: 0 0% 98%; /* White text on primary elements */
    --secondary: 240 3.7% 15.9%; /* Darker Gray for secondary elements */
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%; /* Lighter Gray for muted text */
    --accent: 217.2 91.2% 59.8%; /* Vibrant Blue for accents */
    --accent-foreground: 0 0% 98%;
    --destructive: 0 72.2% 50.6%; /* Adjusted for better visibility on dark */
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%; /* Background of input fields */
    --ring: 217.2 91.2% 59.8%; /* Blue for focus rings */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --radius: 0.5rem;

    /* Sidebar variables updated for consistency with dark theme */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 217.2 91.2% 59.8%; /* Match primary accent */
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Print-specific styles */
@media print {
  body {
    -webkit-print-color-adjust: exact; /* Chrome, Safari, Edge */
    print-color-adjust: exact; /* Firefox */
  }
  body * {
    visibility: hidden !important;
  }
  .printable-chart-area, .printable-chart-area * {
    visibility: visible !important;
  }
  .printable-chart-area {
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: auto !important;
    padding: 20px !important; /* Add some padding for better print layout */
    background-color: hsl(var(--background)) !important; /* Ensure background for dark mode */
    color: hsl(var(--foreground)) !important; /* Ensure text color for dark mode */
  }
  /* Ensure chart elements are visible and styled correctly for printing */
  .printable-chart-area .recharts-wrapper,
  .printable-chart-area .recharts-surface,
  .printable-chart-area .recharts-cartesian-axis-tick-value tspan,
  .printable-chart-area .recharts-text,
  .printable-chart-area .recharts-label tspan,
  .printable-chart-area .recharts-legend-item-text {
    fill: hsl(var(--foreground)) !important; /* Text color for chart elements */
    color: hsl(var(--foreground)) !important;
  }
  .printable-chart-area .recharts-cartesian-grid line {
    stroke: hsl(var(--border)) !important; /* Grid line color */
  }
  .printable-chart-area .recharts-bar-rectangle path {
    fill: hsl(var(--chart-1)) !important; /* Example for bar, adjust as needed */
  }
  .printable-chart-area .recharts-line-curve {
     stroke: hsl(var(--chart-2)) !important; /* Example for line, adjust as needed */
  }
   .printable-chart-area .recharts-pie-sector path {
    stroke: hsl(var(--background)) !important; /* Ensure pie slice borders are visible */
  }

  /* Hide elements that shouldn't be printed within the chart card */
  .printable-chart-area .card-header,
  .printable-chart-area .card-footer,
  .printable-chart-area button,
  .printable-chart-area .print-hide {
    display: none !important;
  }
}
