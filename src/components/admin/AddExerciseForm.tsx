"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Loader2, PlusCircle, CheckCircle2, AlertCircle } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { EXERCISE_CATEGORIES_DIALOG } from "@/lib/mockData";

const addExerciseSchema = z.object({
  name: z
    .string()
    .min(1, "Nazwa ćwiczenia jest wymagana.")
    .max(100, "Nazwa ćwiczenia jest za długa."),
  category: z.string().optional(),
});

type AddExerciseFormValues = z.infer<typeof addExerciseSchema>;

export function AddExerciseForm() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = React.useState(false);
  const [errorMessage, setErrorMessage] = React.useState<string | null>(null);
  const [successMessage, setSuccessMessage] = React.useState<string | null>(
    null
  );

  const form = useForm<AddExerciseFormValues>({
    resolver: zodResolver(addExerciseSchema),
    defaultValues: {
      name: "",
      category: "Inne", // Default category
    },
  });

  const onSubmit = async (values: AddExerciseFormValues) => {
    setIsLoading(true);
    setErrorMessage(null);
    setSuccessMessage(null);

    try {
      const response = await fetch("/api/exercises/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setSuccessMessage(data.message || "Ćwiczenie dodane pomyślnie!");
        toast({
          title: "Sukces!",
          description: data.message || "Ćwiczenie zostało dodane.",
          variant: "default",
        });
        form.reset(); // Clear form after successful submission
      } else {
        setErrorMessage(
          data.message || "Wystąpił błąd podczas dodawania ćwiczenia."
        );
        toast({
          title: "Błąd!",
          description: data.message || "Nie udało się dodać ćwiczenia.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error("Error adding exercise:", error);
      setErrorMessage("Wystąpił nieoczekiwany błąd. Spróbuj ponownie.");
      toast({
        title: "Błąd sieci!",
        description: "Nie udało się połączyć z serwerem.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <PlusCircle className="h-5 w-5 text-green-500" /> Dodaj Nowe Ćwiczenie
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {errorMessage && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Błąd!</AlertTitle>
            <AlertDescription>{errorMessage}</AlertDescription>
          </Alert>
        )}
        {successMessage && !errorMessage && (
          <Alert className="border-green-500 dark:border-green-400">
            <CheckCircle2 className="h-4 w-4 text-green-500 dark:text-green-400" />
            <AlertTitle className="text-green-700 dark:text-green-300">
              Sukces!
            </AlertTitle>
            <AlertDescription className="text-green-700 dark:text-green-300">
              {successMessage}
            </AlertDescription>
          </Alert>
        )}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nazwa Ćwiczenia</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="np. Wyciskanie sztangi"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Kategoria</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isLoading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Wybierz kategorię" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {EXERCISE_CATEGORIES_DIALOG.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                "Dodaj Ćwiczenie"
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
