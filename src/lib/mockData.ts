import type { Exercise as SelectableExerciseType } from "@/components/workout/exercise-selection-dialog";
import type { RecordedSet as WorkoutRecordedSet, ExerciseInWorkout as PlanExerciseInWorkout, Workout as ActiveWorkoutType } from "@/app/(app)/dashboard/workout/active/[workoutId]/page";
import type { SelectableWorkout as PlanSelectableWorkout } from "@/components/plans/select-workout-dialog";

// Import types that are not exported from their original files, then re-export them
import type { RichDayTemplate as ImportedPlanRichDayTemplate } from "@/app/(app)/dashboard/plans/create/page";
export type PlanRichDayTemplate = ImportedPlanRichDayTemplate;

import type { Measurement as ImportedMeasurement } from "@/app/(app)/dashboard/measurements/page";
export type Measurement = ImportedMeasurement;

import type { PersonalBest as ImportedPersonalBest } from "@/app/(app)/dashboard/personal-bests/page";
export type PersonalBest = ImportedPersonalBest;

import type { ProgressPhoto as ImportedProgressPhoto } from "@/app/(app)/dashboard/progress-photos/page";
export type ProgressPhoto = ImportedProgressPhoto;

import type { WellnessEntry as ImportedWellnessEntry } from "@/app/(app)/dashboard/wellness-journal/page.tsx";
export type WellnessEntry = ImportedWellnessEntry;

import type { Portion as ImportedPortion } from "@/components/hydration/add-edit-portion-dialog";
export type Portion = ImportedPortion;

import type { UserGoal as ImportedUserGoal, SimpleHistoricalWorkoutSession as ImportedStatsSimpleHistoricalWorkoutSession, SimpleMeasurement as ImportedStatsSimpleMeasurement, WellnessEntryForStats as ImportedStatsWellnessEntryForStats } from "@/app/(app)/dashboard/statistics/page";
export type UserGoal = ImportedUserGoal;
export type StatsSimpleHistoricalWorkoutSession = ImportedStatsSimpleHistoricalWorkoutSession;
export type StatsSimpleMeasurement = ImportedStatsSimpleMeasurement;
export type StatsWellnessEntryForStats = ImportedStatsWellnessEntryForStats;

import type { MockUser as ImportedFeedMockUser, MockPost as ImportedFeedMockPost, MockNotification as ImportedFeedMockNotification } from "@/app/(app)/dashboard/community/feed/page";
export type FeedMockUser = ImportedFeedMockUser;
export type FeedMockPost = ImportedFeedMockPost;
export type FeedMockNotification = ImportedFeedMockNotification;

import type { RankingUser as ImportedCommunityRankingUser } from "@/app/(app)/dashboard/community/rankings/page"; // Changed from discover page
export type CommunityRankingUser = ImportedCommunityRankingUser;

import type { DiscoverableContent as ImportedCommunityDiscoverableContent } from "@/app/(app)/dashboard/community/discover/page";
export type CommunityDiscoverableContent = ImportedCommunityDiscoverableContent;


// --- Original MOCK_EXERCISES_DATABASE (from workout/create & active workout) ---
export const INITIAL_MOCK_EXERCISES_DATABASE_DATA: SelectableExerciseType[] = [
  { id: "ex1", name: "Wyciskanie sztangi na ławce płaskiej", category: "Klatka", instructions: "Opuść sztangę do klatki piersiowej, a następnie dynamicznie wypchnij w górę. Łokcie prowadź pod kątem około 45 stopni względem tułowia.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex2", name: "Przysiady ze sztangą", category: "Nogi", instructions: "Sztangę trzymaj na barkach (low-bar lub high-bar). Zejdź co najmniej do momentu, gdy uda będą równoległe do podłoża. Kolana prowadź na zewnątrz, w linii ze stopami.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex3", name: "Martwy ciąg", category: "Plecy", instructions: "Podejdź do sztangi tak, aby piszczele jej dotykały. Chwyć sztangę nachwytem lub chwytem mieszanym. Utrzymuj proste plecy i podnieś ciężar, prostując biodra i kolana jednocześnie.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex4", name: "Podciąganie na drążku", category: "Plecy", instructions: "Chwyć drążek nachwytem, nieco szerzej niż szerokość barków. Podciągnij się, aż broda znajdzie się nad drążkiem. Kontrolowanie opuszczaj ciało.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex5", name: "Pompki", category: "Klatka", instructions: "Dłonie rozstaw na szerokość barków. Ciało powinno tworzyć prostą linię od głowy do pięt. Opuść klatkę piersiową nisko nad podłogę, a następnie wypchnij się w górę.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex6", name: "Bieg na bieżni", category: "Cardio", instructions: "Ustaw odpowiednią prędkość i nachylenie. Utrzymuj stałe tempo lub wykonuj interwały.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex7", name: "Skakanka", category: "Cardio", instructions: "Skacz rytmicznie, utrzymując lekko ugięte kolana. Kręć skakanką za pomocą nadgarstków.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex8", name: "Rozciąganie dynamiczne", category: "Całe ciało", instructions: "Wykonuj płynne ruchy rozciągające główne grupy mięśniowe, np. wymachy ramion, krążenia bioder, wykroki z rotacją.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex9", name: "Wyciskanie żołnierskie (OHP)", category: "Barki", instructions: "Sztangę trzymaj na wysokości obojczyków. Wypchnij ciężar pionowo nad głowę, blokując łokcie. Kontrolowanie opuść.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex10", name: "Uginanie ramion ze sztangą", category: "Ramiona", instructions: "Trzymaj sztangę podchwytem na szerokość barków. Uginaj ramiona w łokciach, unosząc ciężar do wysokości barków. Unikaj bujania tułowiem.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex11", name: "Plank", category: "Brzuch", instructions: "Oprzyj się na przedramieniach i palcach stóp. Ciało powinno tworzyć prostą linię. Napnij mięśnie brzucha i pośladków.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex12", name: "Wiosłowanie sztangą", category: "Plecy", instructions: "Pochyl tułów, utrzymując proste plecy. Chwyć sztangę nachwytem i przyciągaj ją do dolnej części brzucha, ściągając łopatki.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex13", name: "Wykroki", category: "Nogi", instructions: "Zrób duży krok w przód i opuść biodra, aż oba kolana będą zgięte pod kątem 90 stopni. Wróć do pozycji wyjściowej i powtórz drugą nogą.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex14", name: "Unoszenie hantli bokiem", category: "Barki", instructions: "Trzymając hantle w dłoniach, unoś ramiona na boki do wysokości barków. Łokcie lekko ugięte.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex15", name: "Francuskie wyciskanie sztangielki", category: "Ramiona", instructions: "Trzymaj sztangielkę oburącz za głową. Prostuj ramiona, unosząc ciężar nad głowę. Skup się na pracy tricepsów.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex16", name: "Allah Pompki (Modlitewniki)", category: "Brzuch", instructions: "Klęcząc, oprzyj łokcie na podłodze. Zbliżaj głowę do podłogi, zaokrąglając plecy i napinając brzuch.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex17", name: "Przysiad bułgarski", category: "Nogi", instructions: "Jedna noga oparta z tyłu na podwyższeniu. Wykonuj przysiad na nodze wykrocznej, schodząc nisko.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex18", name: "Wyciskanie hantli na ławce skośnej", category: "Klatka", instructions: "Ustaw ławkę pod kątem 30-45 stopni. Wyciskaj hantle, skupiając się na górnej części klatki piersiowej.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex19", name: "Orbitrek (30 min)", category: "Cardio", instructions: "Utrzymuj płynne ruchy, angażując zarówno nogi, jak i ramiona. Dostosuj opór do swoich możliwości.", videoUrl: "https://www.youtube.com/watch?v=example"},
  { id: "ex20", name: "Wall sit (60s)", category: "Nogi", instructions: "Oprzyj plecy o ścianę, zsuwając się, aż uda będą równoległe do podłogi. Utrzymaj pozycję przez określony czas.", videoUrl: "https://www.youtube.com/watch?v=example"},
];
export { INITIAL_MOCK_EXERCISES_DATABASE_DATA as MOCK_EXERCISES_DATABASE };
export type { SelectableExerciseType };

// --- User Profile Data (from src/app/(app)/dashboard/profile/[userId]/page.tsx) ---
export interface UserProfile {
  id: string;
  fullName: string;
  username: string;
  email: string;
  avatarUrl: string;
  bio?: string;
  fitnessLevel: "Początkujący" | "Średniozaawansowany" | "Zaawansowany" | "Ekspert";
  joinDate: string; // ISO string
  followers: number;
  following: number;
  region?: string; // Added from community/discover
  recentActivity: Array<{
    id: string;
    type: "workout" | "post" | "achievement" | "plan_completed";
    title: string;
    timestamp: string; // ISO string
    details?: string;
    link?: string;
  }>;
  linkedSocialAccounts?: {
    google?: boolean;
    facebook?: boolean;
  };
  privacySettings?: {
    isActivityPublic: boolean;
    isFriendsListPublic: boolean;
    isSharedPlansPublic: boolean;
  };
  role?: 'client' | 'trener' | 'admin';
  dateOfBirth?: string; // ISO string
  gender?: "male" | "female" | "other" | "prefer_not-to-say";
  weight?: number;
  height?: number;
}

export const MOCK_USER_PROFILES_DB: UserProfile[] = [
  {
    id: "user1",
    fullName: "Aleksandra Nowicka",
    username: "alex_fit_girl",
    email: "<EMAIL>",
    avatarUrl: "https://placehold.co/200x200.png?text=AN",
    bio: "Miłośniczka crossfitu i zdrowego stylu życia. W ciągłym ruchu, zawsze gotowa na nowe wyzwanie!",
    fitnessLevel: "Zaawansowany",
    joinDate: new Date(2022, 5, 15).toISOString(),
    followers: 1250,
    following: 300,
    region: "Mazowieckie",
    recentActivity: [
      { id: "act1", type: "workout", title: "Poranny Crossfit WOD", timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), details: "Ukończono 'Fran' w 5:30", link: "/dashboard/history/some-id1" },
      { id: "act2", type: "post", title: "Nowy przepis na proteinowe smoothie!", timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), details: "Idealne po ciężkim treningu...", link: "/dashboard/community/feed/post-id-1" },
      { id: "act3", type: "achievement", title: "Osiągnięto 500 treningów!", timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString(), details: "Jubileuszowy trening zaliczony." },
    ],
    linkedSocialAccounts: { google: true },
    privacySettings: { isActivityPublic: true, isFriendsListPublic: true, isSharedPlansPublic: true},
    role: 'client',
    dateOfBirth: new Date(1995, 8, 20).toISOString(),
    gender: "female",
  },
  {
    id: "user2",
    fullName: "Krzysztof Kowalski",
    username: "kris_trener",
    email: "<EMAIL>",
    avatarUrl: "https://placehold.co/200x200.png?text=KK",
    bio: "Certyfikowany trener personalny, specjalista od budowania siły i masy mięśniowej. Pomagam innym osiągać ich cele!",
    fitnessLevel: "Ekspert",
    joinDate: new Date(2021, 1, 10).toISOString(),
    followers: 5200,
    following: 150,
    region: "Małopolskie",
    recentActivity: [
      { id: "act4", type: "plan_completed", title: "Ukończono plan 'Masa XTREME'", timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString(), details: "8 tygodni ciężkiej pracy zaowocowało +5kg masy!", link: "/dashboard/plans/plan-xtreme" },
      { id: "act5", type: "post", title: "Technika martwego ciągu - najczęstsze błędy", timestamp: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(), details: "Sprawdź, czy nie popełniasz tych błędów.", link: "/dashboard/community/feed/post-id-2" },
    ],
    linkedSocialAccounts: { facebook: true },
    privacySettings: { isActivityPublic: true, isFriendsListPublic: false, isSharedPlansPublic: true},
    role: 'trener',
  },
  {
    id: "user3",
    fullName: "Zofia Wójcik",
    username: "zofia_yoga_life",
    email: "<EMAIL>",
    avatarUrl: "https://placehold.co/200x200.png?text=ZW",
    bio: "Instruktorka jogi i medytacji. Szukam harmonii między ciałem a umysłem. Namaste.",
    fitnessLevel: "Średniozaawansowany",
    joinDate: new Date(2023, 0, 5).toISOString(),
    followers: 800,
    following: 200,
    region: "Śląskie",
    recentActivity: [
       { id: "act6", type: "workout", title: "Poranna sesja Vinyasa Jogi", timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(), details: "60 minut płynnej praktyki", link: "/dashboard/history/some-id2" },
    ],
    privacySettings: { isActivityPublic: false, isFriendsListPublic: true, isSharedPlansPublic: false},
    role: 'client',
  },
];

export const MOCK_CURRENT_USER_PROFILE: UserProfile = {
  id: "current_user_id",
  fullName: "Jan Testowy",
  username: "jan_tester",
  email: "<EMAIL>",
  avatarUrl: "https://placehold.co/200x200.png?text=JT",
  bio: "Aktywnie testuję LeniwaKluska! Lubię siłownię i bieganie.",
  fitnessLevel: "Średniozaawansowany",
  joinDate: new Date(2023, 8, 1).toISOString(),
  followers: 150,
  following: 75,
  recentActivity: [
    { id: "act7", type: "workout", title: "Wieczorny trening siłowy", timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(), details: "FBW, 3 serie po 10 powtórzeń", link: "/dashboard/history/some-id3" },
    { id: "act8", type: "achievement", title: "Osiągnięto nowy rekord w przysiadzie!", timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(), details: "100kg x 5!", link: "/dashboard/personal-bests" },
  ],
  linkedSocialAccounts: { google: true, facebook: false },
  privacySettings: { isActivityPublic: true, isFriendsListPublic: true, isSharedPlansPublic: true},
  role: 'admin',
  dateOfBirth: new Date(1992, 3, 10).toISOString(),
  gender: "male",
  weight: 80,
  height: 182,
};
// Ensure current user is part of the discoverable DB if not already
if (!MOCK_USER_PROFILES_DB.find(u => u.id === MOCK_CURRENT_USER_PROFILE.id)) {
    MOCK_USER_PROFILES_DB.push(MOCK_CURRENT_USER_PROFILE);
}


// --- Workout History Data ---
export enum DifficultyRating {
  BardzoLatwy = "Bardzo Łatwy",
  Latwy = "Łatwy",
  Sredni = "Średni",
  Trudny = "Trudny",
  BardzoTrudny = "Bardzo Trudny",
  Ekstremalny = "Ekstremalny",
}
export type RecordedSet = WorkoutRecordedSet;
export type ExerciseInWorkout = PlanExerciseInWorkout;

export interface HistoricalWorkoutSession {
  id: string;
  workoutId: string;
  workoutName: string;
  workoutType: string;
  startTime: string; // ISO string
  endTime: string; // ISO string
  totalTimeSeconds: number;
  recordedSets: Record<string, RecordedSet[]>; // Key is exerciseId
  exercises: ExerciseInWorkout[]; // List of exercises in the original plan
  difficulty?: DifficultyRating;
  generalNotes?: string;
  calculatedTotalVolume: number;
  userId?: string; // For associating with a user
}

export const MOCK_HISTORY_SESSIONS: HistoricalWorkoutSession[] = [
  {
    id: "hist1",
    workoutId: "wk1",
    workoutName: "Poranny Trening Siłowy",
    workoutType: "Siłowy",
    startTime: "2024-07-25T08:00:00.000Z",
    endTime: "2024-07-25T09:00:00.000Z",
    totalTimeSeconds: 3600,
    recordedSets: {
      ex1: [{ setNumber: 1, weight: "60", reps: "10", rpe: 7, notes: "Good form" }, { setNumber: 2, weight: "65", reps: "8", rpe: 8 }],
      ex2: [{ setNumber: 1, weight: "100", reps: "5", rpe: 9, notes: "Heavy but okay" }],
      ex4: [{ setNumber: 1, weight: "BW", reps: "8", rpe: 7 }, { setNumber: 2, weight: "BW", reps: "6", rpe: 8, notes: "Trochę zmęczony" }],
    },
    exercises: [
      { id: "ex1", name: "Wyciskanie sztangi na ławce płaskiej", defaultSets: 3, defaultReps: "8-10", defaultRest: 90 },
      { id: "ex2", name: "Przysiady ze sztangą", defaultSets: 4, defaultReps: "6-8", defaultRest: 120 },
      { id: "ex4", name: "Podciąganie na drążku", defaultSets: 3, defaultReps: "Max", defaultRest: 90 },
    ],
    difficulty: DifficultyRating.Sredni,
    generalNotes: "Feeling strong today! Focused on technique. Może następnym razem dodam ciężaru w przysiadach.",
    calculatedTotalVolume: (60*10) + (65*8) + (100*5),
    userId: "current_user_id"
  },
  {
    id: "hist2",
    workoutId: "wk2",
    workoutName: "Szybkie Cardio HIIT",
    workoutType: "Cardio",
    startTime: "2024-07-27T17:30:00.000Z",
    endTime: "2024-07-27T18:00:00.000Z",
    totalTimeSeconds: 1800,
    recordedSets: {
      ex6: [{ setNumber: 1, weight: "N/A", reps: "30 min", rpe: 8 }],
    },
    exercises: [{ id: "ex6", name: "Bieg na bieżni (30 min)", defaultSets: 1, defaultReps: "30 min", defaultRest: 0 }],
    difficulty: DifficultyRating.Trudny,
    generalNotes: "Tough session, pushed hard on intervals.",
    calculatedTotalVolume: 0,
    userId: "current_user_id"
  },
  {
    id: "hist3",
    workoutId: "wk1",
    workoutName: "Poranny Trening Siłowy",
    workoutType: "Siłowy",
    startTime: "2024-07-29T08:15:00.000Z",
    endTime: "2024-07-29T09:20:00.000Z",
    totalTimeSeconds: 3900,
    recordedSets: {
      ex1: [{ setNumber: 1, weight: "65", reps: "10", rpe: 7 }, { setNumber: 2, weight: "70", reps: "8", rpe: 8}, { setNumber: 3, weight: "70", reps: "7", rpe: 8.5, notes: "Ostatnie ciężko"}],
      ex2: [{ setNumber: 1, weight: "100", reps: "6", rpe: 8 }, { setNumber: 2, weight: "105", reps: "5", rpe: 9, notes: "Nowy PR!"}],
    },
    exercises: [
      { id: "ex1", name: "Wyciskanie sztangi na ławce płaskiej", defaultSets: 3, defaultReps: "8-10", defaultRest: 90 },
      { id: "ex2", name: "Przysiady ze sztangą", defaultSets: 3, defaultReps: "5-8", defaultRest: 120 },
    ],
    difficulty: DifficultyRating.Sredni,
    calculatedTotalVolume: (65*10) + (70*8) + (70*7) + (100*6) + (105*5),
    userId: "current_user_id"
  },
  { id: "hist4", workoutId: "wk2", workoutName: "Cardio Popołudniowe", workoutType: "Cardio", startTime: "2024-07-10T16:00:00.000Z", endTime: "2024-07-10T16:45:00.000Z", totalTimeSeconds: 2700, recordedSets: {ex7: [{setNumber: 1, weight: "N/A", reps: "15 min"}]}, exercises: [{id: "ex7", name: "Skakanka (15 min)"}], calculatedTotalVolume: 0, difficulty: DifficultyRating.Latwy, userId: "current_user_id" },
  { id: "hist5", workoutId: "wkCustom1", workoutName: "Trening Siłowy - Nogi", workoutType: "Siłowy", startTime: "2024-07-10T09:00:00.000Z", endTime: "2024-07-10T10:15:00.000Z", totalTimeSeconds: 4500, recordedSets: {ex2: [{setNumber: 1, weight: 80, reps: 10}], ex13: [{setNumber: 1, weight: "20kg each", reps: 12}]}, exercises: [{id: "ex2", name: "Przysiady ze sztangą"}, {id: "ex13", name: "Wykroki"}], calculatedTotalVolume: 12000, difficulty: DifficultyRating.Trudny, userId: "current_user_id" },
  { id: "hist6", workoutId: "wk3", workoutName: "Joga Poranna", workoutType: "Rozciąganie", startTime: "2024-07-18T07:00:00.000Z", endTime: "2024-07-18T07:30:00.000Z", totalTimeSeconds: 1800, recordedSets: {ex8: [{setNumber: 1, weight: "N/A", reps: "30 min"}]}, exercises: [{id:"ex8", name:"Rozciąganie dynamiczne"}], calculatedTotalVolume: 0, difficulty: DifficultyRating.BardzoLatwy, userId: "current_user_id" },
  { id: "hist7", workoutId: "wkCustom2", workoutName: "Trening Mieszany - Całe Ciało", workoutType: "Mieszany", startTime: "2024-08-05T18:00:00.000Z", endTime: "2024-08-05T19:00:00.000Z", totalTimeSeconds: 3600, recordedSets: {ex1: [{setNumber:1, weight: 50, reps: 12}], ex6: [{setNumber:1, weight: "N/A", reps: "20 min"}]}, exercises: [{id: "ex1", name: "Wyciskanie sztangi na ławce płaskiej"}, {id: "ex6", name: "Bieg na bieżni"}], calculatedTotalVolume: 8000, difficulty: DifficultyRating.Sredni, userId: "current_user_id" },
  { id: "hist8", workoutId: "wkCustom3", workoutName: "Siłówka Wieczorna", workoutType: "Siłowy", startTime: "2024-08-15T20:00:00.000Z", endTime: "2024-08-15T21:15:00.000Z", totalTimeSeconds: 4500, recordedSets: {ex3: [{setNumber:1, weight:120, reps:5}], ex12: [{setNumber:1, weight:70, reps:8}]}, exercises: [{id:"ex3", name:"Martwy ciąg"}, {id:"ex12", name:"Wiosłowanie sztangą"}], calculatedTotalVolume: 15000, difficulty: DifficultyRating.Trudny, userId: "current_user_id" },
];

// --- Training Plan Detail Data ---
export interface PlanDayDetail {
  dayName: string;
  assignedWorkoutId?: string;
  assignedWorkoutName?: string;
  isRestDay: boolean;
  notes?: string;
  templateId?: string | null;
  templateName?: string | null;
}
export interface DetailedTrainingPlan {
  id: string;
  name: string;
  description: string;
  goal: string;
  duration: string;
  schedule: PlanDayDetail[];
  author?: string;
  isPublic?: boolean;
  startDate?: string; // ISO string, optional
  endDate?: string; // ISO string, optional
}
export const MOCK_DETAILED_TRAINING_PLANS: DetailedTrainingPlan[] = [
  {
    id: 'plan1',
    name: 'Siła Początkującego Herkulesa (Detale)',
    description: 'Kompleksowy plan dla osób rozpoczynających przygodę z treningiem siłowym, skupiony na podstawowych ćwiczeniach wielostawowych. Ten plan zakłada 3 dni treningowe w tygodniu i 4 dni odpoczynku.',
    goal: 'Budowa siły',
    duration: '8 tygodni',
    author: 'Krzysztof Trener',
    isPublic: true,
    startDate: new Date(2024, 6, 1).toISOString(),
    endDate: new Date(2024, 8, 23).toISOString(),
    schedule: [
      { dayName: "Poniedziałek", assignedWorkoutId: "wk1", assignedWorkoutName: "Trening A - Full Body (Wyciskanie, Przysiady, Podciąganie)", isRestDay: false, notes: "Skup się na technice, nie na ciężarze." },
      { dayName: "Wtorek", isRestDay: true, notes: "Aktywny odpoczynek: spacer lub lekkie rozciąganie." },
      { dayName: "Środa", assignedWorkoutId: "custom_wk_fb_b", assignedWorkoutName: "Trening B - Full Body (OHP, Martwy Ciąg, Wiosłowanie)", isRestDay: false, notes: "Utrzymaj napięcie mięśniowe." },
      { dayName: "Czwartek", isRestDay: true },
      { dayName: "Piątek", assignedWorkoutId: "custom_wk_fb_c", assignedWorkoutName: "Trening C - Full Body (Warianty ćwiczeń A i B, Akcesoria)", isRestDay: false, notes: "Możesz spróbować dodać minimalnie ciężaru." },
      { dayName: "Sobota", isRestDay: true, notes: "Dłuższy spacer, regeneracja." },
      { dayName: "Niedziela", isRestDay: true, notes: "Pełna regeneracja" },
    ],
  },
  {
    id: 'plan2',
    name: 'Kardio Spalacz Kalorii (Detale)',
    description: 'Intensywny plan kardio interwałowego i aerobowego, mający na celu maksymalizację spalania kalorii i poprawę wydolności. Zaplanowane 5 sesji kardio w tygodniu.',
    goal: 'Redukcja tkanki tłuszczowej',
    duration: '6 tygodni',
    author: 'Aleksandra Fit',
    isPublic: true,
    schedule: [
      { day

</final_file_content>

IMPORTANT: For any future changes to this file, use the final_file_content shown above as your reference. This content reflects the current state of the file, including any auto-formatting (e.g., if you used single quotes but the formatter converted them to double quotes). Always base your SEARCH/REPLACE operations on this final version to ensure accuracy.



New problems detected after saving the file:
src/lib/mockData.ts
- [ts Error] Line 346: '}' expected.
- [ts Error] Line 24: Module '"@/app/(app)/dashboard/statistics/page"' has no exported member 'SimpleHistoricalWorkoutSession'. Did you mean to use 'import SimpleHistoricalWorkoutSession from "@/app/(app)/dashboard/statistics/page"' instead?
- [ts Error] Line 24: Module '"@/app/(app)/dashboard/statistics/page"' has no exported member 'SimpleMeasurement'. Did you mean to use 'import SimpleMeasurement from "@/app/(app)/dashboard/statistics/page"' instead?
- [ts Error] Line 24: Module '"@/app/(app)/dashboard/statistics/page"' has no exported member 'WellnessEntryForStats'. Did you mean to use 'import WellnessEntryForStats from "@/app/(app)/dashboard/statistics/page"' instead?
- [ts Error] Line 30: Module '"@/app/(app)/dashboard/community/feed/page"' has no exported member 'MockUser'. Did you mean to use 'import MockUser from "@/app/(app)/dashboard/community/feed/page"' instead?
- [ts Error] Line 30: Module '"@/app/(app)/dashboard/community/feed/page"' has no exported member 'MockPost'. Did you mean to use 'import MockPost from "@/app/(app)/dashboard/community/feed/page"' instead?
- [ts Error] Line 30: Module '"@/app/(app)/dashboard/community/feed/page"' has no exported member 'MockNotification'. Did you mean to use 'import MockNotification from "@/app/(app)/dashboard/community/feed/page"' instead?
- [ts Error] Line 35: Module '"@/app/(app)/dashboard/community/rankings/page"' has no exported member 'RankingUser'. Did you mean to use 'import RankingUser from "@/app/(app)/dashboard/community/rankings/page"' instead?
- [ts Error] Line 38: Module '"@/app/(app)/dashboard/community/discover/page"' has no exported member 'DiscoverableContent'. Did you mean to use 'import DiscoverableContent from "@/app/(app)/dashboard/community/discover/page"' instead?
- [ts Error] Line 44: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 45: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 46: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 47: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 48: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 49: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 50: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 51: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 52: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 53: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 54: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 55: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 56: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 57: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 58: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 59: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 60: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 61: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 62: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 63: Object literal may only specify known properties, and 'instructions' does not exist in type 'Exercise'.
- [ts Error] Line 346: Object literal may only specify known properties, and 'day' does not exist in type 'PlanDayDetail'.
- [ts Error] Line 346: No value exists in scope for the shorthand property 'day'. Either declare one or provide an initializer.<environment_details>
# VSCode Visible Files
src/lib/mockData.ts

# VSCode Open Tabs
README.md
postcss.config.mjs
src/app/measurements/page.tsx
src/app/personal-bests/page.tsx
src/context/ProgressionSettingsContext.tsx
src/app/workout/active/[workoutId]/page.tsx
src/pages/api/auth/login.ts
src/components/auth/login-form.tsx
src/pages/api/user/profile.ts
src/lib/database.ts
src/pages/api/auth/register.ts
src/components/auth/registration-form.tsx
src/app/account/page.tsx
src/pages/api/exercises/index.ts
src/components/admin/AddExerciseForm.tsx
src/app/(app)/dashboard/workout/create/page.tsx
src/pages/api/user/current.ts
src/pages/api/user/[userId].ts
src/app/(app)/dashboard/profile/[userId]/page.tsx
src/pages/api/users/index.ts
src/app/(app)/dashboard/admin/debug/page.tsx
src/app/(app)/dashboard/community/discover/page.tsx
src/lib/sqlite.ts
src/lib/mockData.ts
src/components/workout/exercise-selection-dialog.tsx

# Current Time
25.05.2025, 11:23:50 AM (Europe/Warsaw, UTC+2:00)

# Context Window Usage
885 643 / 1048,576K tokens used (84%)

# Current Mode
ACT MODE
</environment_details>
